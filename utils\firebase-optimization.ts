/**
 * Firebase query optimization utilities
 */

import { 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs, 
  doc, 
  getDoc,
  QuerySnapshot,
  DocumentData,
  Query,
  writeBatch,
  FirestoreError
} from 'firebase/firestore';
import { firestore } from '@/config/firebase';
import { startPerformanceTimer, endPerformanceTimer } from './memory-utils';

// Cache for query results
const queryCache = new Map<string, { data: any; timestamp: number; ttl: number }>();

// Default cache TTL (5 minutes)
const DEFAULT_CACHE_TTL = 5 * 60 * 1000;

/**
 * Generate cache key for queries
 */
const generateCacheKey = (collectionPath: string, queryParams?: any): string => {
  return `${collectionPath}_${JSON.stringify(queryParams || {})}`;
};

/**
 * Check if cached data is still valid
 */
const isCacheValid = (cacheEntry: { timestamp: number; ttl: number }): boolean => {
  return Date.now() - cacheEntry.timestamp < cacheEntry.ttl;
};

/**
 * Optimized Firebase query with caching and fallback
 */
export const optimizedQuery = async <T = DocumentData>(
  collectionPath: string,
  queryConstraints: any[] = [],
  options: {
    useCache?: boolean;
    cacheTTL?: number;
    fallbackToSimple?: boolean;
    performanceLabel?: string;
  } = {}
): Promise<T[]> => {
  const {
    useCache = true,
    cacheTTL = DEFAULT_CACHE_TTL,
    fallbackToSimple = true,
    performanceLabel = `Query_${collectionPath}`
  } = options;

  startPerformanceTimer(performanceLabel);

  // Check cache first
  if (useCache) {
    const cacheKey = generateCacheKey(collectionPath, queryConstraints);
    const cached = queryCache.get(cacheKey);
    
    if (cached && isCacheValid(cached)) {
      endPerformanceTimer(performanceLabel);
      console.log(`[Firebase] Cache hit for ${collectionPath}`);
      return cached.data;
    }
  }

  try {
    // Try optimized query first
    const collectionRef = collection(firestore, collectionPath);
    let q: Query = collectionRef;

    // Apply query constraints
    if (queryConstraints.length > 0) {
      q = query(collectionRef, ...queryConstraints);
    }

    const querySnapshot = await getDocs(q);
    const results: T[] = [];

    querySnapshot.forEach((doc) => {
      results.push({ id: doc.id, ...doc.data() } as T);
    });

    // Cache the results
    if (useCache) {
      const cacheKey = generateCacheKey(collectionPath, queryConstraints);
      queryCache.set(cacheKey, {
        data: results,
        timestamp: Date.now(),
        ttl: cacheTTL
      });
    }

    endPerformanceTimer(performanceLabel);
    console.log(`[Firebase] Query successful for ${collectionPath}: ${results.length} documents`);
    return results;

  } catch (error) {
    console.warn(`[Firebase] Optimized query failed for ${collectionPath}:`, error);

    if (fallbackToSimple && (error as FirestoreError).code === 'failed-precondition') {
      console.log(`[Firebase] Falling back to simple query for ${collectionPath}`);
      
      try {
        // Fallback to simple query without complex constraints
        const collectionRef = collection(firestore, collectionPath);
        const querySnapshot = await getDocs(collectionRef);
        const results: T[] = [];

        querySnapshot.forEach((doc) => {
          results.push({ id: doc.id, ...doc.data() } as T);
        });

        // Cache the fallback results with shorter TTL
        if (useCache) {
          const cacheKey = generateCacheKey(collectionPath, 'fallback');
          queryCache.set(cacheKey, {
            data: results,
            timestamp: Date.now(),
            ttl: cacheTTL / 2 // Shorter TTL for fallback queries
          });
        }

        endPerformanceTimer(performanceLabel);
        console.log(`[Firebase] Fallback query successful for ${collectionPath}: ${results.length} documents`);
        return results;

      } catch (fallbackError) {
        endPerformanceTimer(performanceLabel);
        console.error(`[Firebase] Both optimized and fallback queries failed for ${collectionPath}:`, fallbackError);
        throw fallbackError;
      }
    }

    endPerformanceTimer(performanceLabel);
    throw error;
  }
};

/**
 * Batch fetch multiple documents efficiently
 */
export const batchFetchDocuments = async <T = DocumentData>(
  documentPaths: { collection: string; id: string }[],
  options: { useCache?: boolean; cacheTTL?: number } = {}
): Promise<(T | null)[]> => {
  const { useCache = true, cacheTTL = DEFAULT_CACHE_TTL } = options;
  
  startPerformanceTimer('BatchFetch');
  
  const results: (T | null)[] = [];
  const uncachedPaths: { collection: string; id: string; index: number }[] = [];

  // Check cache for each document
  for (let i = 0; i < documentPaths.length; i++) {
    const path = documentPaths[i];
    const cacheKey = `${path.collection}/${path.id}`;
    
    if (useCache) {
      const cached = queryCache.get(cacheKey);
      if (cached && isCacheValid(cached)) {
        results[i] = cached.data;
        continue;
      }
    }
    
    uncachedPaths.push({ ...path, index: i });
    results[i] = null; // Placeholder
  }

  // Fetch uncached documents
  if (uncachedPaths.length > 0) {
    const fetchPromises = uncachedPaths.map(async (path) => {
      try {
        const docRef = doc(firestore, path.collection, path.id);
        const docSnap = await getDoc(docRef);
        
        if (docSnap.exists()) {
          const data = { id: docSnap.id, ...docSnap.data() } as T;
          
          // Cache the result
          if (useCache) {
            const cacheKey = `${path.collection}/${path.id}`;
            queryCache.set(cacheKey, {
              data,
              timestamp: Date.now(),
              ttl: cacheTTL
            });
          }
          
          return { data, index: path.index };
        }
        return { data: null, index: path.index };
      } catch (error) {
        console.error(`[Firebase] Error fetching document ${path.collection}/${path.id}:`, error);
        return { data: null, index: path.index };
      }
    });

    const fetchResults = await Promise.all(fetchPromises);
    
    // Update results array
    fetchResults.forEach(({ data, index }) => {
      results[index] = data;
    });
  }

  endPerformanceTimer('BatchFetch');
  console.log(`[Firebase] Batch fetch completed: ${documentPaths.length} documents, ${uncachedPaths.length} fetched from server`);
  
  return results;
};

/**
 * Clear cache for specific collection or all cache
 */
export const clearQueryCache = (collectionPath?: string) => {
  if (collectionPath) {
    // Clear cache entries for specific collection
    const keysToDelete: string[] = [];
    queryCache.forEach((_, key) => {
      if (key.startsWith(collectionPath)) {
        keysToDelete.push(key);
      }
    });
    keysToDelete.forEach(key => queryCache.delete(key));
    console.log(`[Firebase] Cleared cache for ${collectionPath}: ${keysToDelete.length} entries`);
  } else {
    // Clear all cache
    const size = queryCache.size;
    queryCache.clear();
    console.log(`[Firebase] Cleared all cache: ${size} entries`);
  }
};

/**
 * Get cache statistics
 */
export const getCacheStats = () => {
  const stats = {
    totalEntries: queryCache.size,
    validEntries: 0,
    expiredEntries: 0
  };

  queryCache.forEach((entry) => {
    if (isCacheValid(entry)) {
      stats.validEntries++;
    } else {
      stats.expiredEntries++;
    }
  });

  return stats;
};

/**
 * Clean expired cache entries
 */
export const cleanExpiredCache = () => {
  const keysToDelete: string[] = [];
  
  queryCache.forEach((entry, key) => {
    if (!isCacheValid(entry)) {
      keysToDelete.push(key);
    }
  });

  keysToDelete.forEach(key => queryCache.delete(key));
  console.log(`[Firebase] Cleaned expired cache entries: ${keysToDelete.length}`);
  
  return keysToDelete.length;
};
