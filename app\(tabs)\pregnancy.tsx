import React, { useEffect, useState, useMemo } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useThemeColors } from '../../hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import PregnancyCard from '@/components/AnimalDetails/PregnancyCard';
import { PlusIcon, Calendar, MapPin } from 'lucide-react-native';
import { usePregnancyStore } from '@/store/pregnancy-store';
import { useFarmStore } from '@/store/farm-store';
import GenericDropdown from '@/components/GenericDropdown';
import { useAnimalStore } from '@/store/animal-store';
import { useAuthStore } from '@/store/auth-store';
import { useFocusEffect } from 'expo-router';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
// import DebugInfo from '@/components/DebugInfo'; // Uncomment for debugging

const PregnancyListScreen = () => {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  const router = useRouter();
  const params = useLocalSearchParams();
  const { farms, fetchFarms, isLoading: farmsLoading } = useFarmStore();
  const { pregnancies, loading: pregnanciesLoading, fetchPregnancies } = usePregnancyStore();
  const { animals, isLoading: animalsLoading, fetchAnimalsByFarm } = useAnimalStore();
  const { user } = useAuthStore();

  // Initialize with params if provided
  const [selectedFarm, setSelectedFarm] = useState<string | null>(params.farmId as string || null);
  const [selectedFemaleAnimal, setSelectedFemaleAnimal] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [initialAnimalId] = useState<string | null>(params.animalId as string || null);

  // Handle navigation to pregnancy details
  const handlePregnancyPress = (pregnancy: any) => {
    router.push(`/pregnancy/details/${pregnancy.id}`);
  };

  // Load farms when component mounts
  useEffect(() => {
    const loadInitialData = async () => {
      if (user) {
        try {
          setLoading(true);
          await fetchFarms(user.id);
          setLoading(false);
        } catch (error) {
          console.error('Error loading farms:', error);
          setLoading(false);
        }
      }
    };
    
    loadInitialData();
  }, [user, fetchFarms]);

  // Refresh data when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      const refreshData = async () => {
        if (user) {
          try {
            setLoading(true);
            await fetchFarms(user.id);
            
            // If a farm is selected, refresh its data
            if (selectedFarm) {
              if (selectedFarm === 'all') {
                // Refresh data from all farms
                const allFarmPromises = farms.map(farm =>
                  Promise.all([
                    fetchPregnancies(farm.id),
                    fetchAnimalsByFarm(farm.id)
                  ])
                );
                await Promise.all(allFarmPromises);
              } else {
                await Promise.all([
                  fetchPregnancies(selectedFarm),
                  fetchAnimalsByFarm(selectedFarm)
                ]);
              }
            }
          } catch (error) {
            console.error('Error refreshing data on focus:', error);
          } finally {
            setLoading(false);
          }
        }
      };
      
      refreshData();
      
      return () => {};
    }, [user, fetchFarms, selectedFarm, fetchPregnancies, fetchAnimalsByFarm])
  );

  // Memoize farm options to prevent infinite re-renders
  const farmOptions = useMemo(() => {
    if (farms && farms.length > 0) {
      return [
        {
          id: 'all',
          label: t('common.allFarms', { defaultValue: 'All Farms' }),
          icon: <MapPin size={20} color={themedColors.primary} />
        },
        ...farms.map(farm => ({
          id: farm.id,
          label: farm.name,
          description: typeof farm.location === 'string' ? farm.location : farm.location?.address || 'No location',
          icon: <MapPin size={20} color={themedColors.primary} />
        }))
      ];
    }
    return [];
  }, [farms, t, themedColors.primary]);

  // Auto-select "All Farms" when farms are available and none is selected
  useEffect(() => {
    if (farmOptions.length > 1 && !selectedFarm) {
      setSelectedFarm('all');
    } else if (farmOptions.length === 0 && selectedFarm) {
      setSelectedFarm(null);
    }
  }, [farmOptions.length, selectedFarm]);
  
  // Fetch pregnancies and animals when farm is selected
  useEffect(() => {
    if (selectedFarm && farms.length > 0) {
      setLoading(true);

      // Add timeout to prevent infinite loading
      const timeoutId = setTimeout(() => {
        setLoading(false);
      }, 30000); // 30 second timeout

      if (selectedFarm === 'all') {
        // Fetch data from all farms
        if (farms.length === 0) {
          setLoading(false);
          clearTimeout(timeoutId);
          return;
        }

        const allFarmPromises = farms.map(farm =>
          Promise.all([
            fetchPregnancies(farm.id),
            fetchAnimalsByFarm(farm.id)
          ])
        );

        Promise.all(allFarmPromises)
        .then(() => {
          console.log('Successfully fetched pregnancies and animals from all farms');
        })
        .catch(error => {
          console.error('Error fetching all farms data:', error);
        })
        .finally(() => {
          clearTimeout(timeoutId);
          setLoading(false);
        });
      } else {
        // Fetch data from specific farm
        Promise.all([
          fetchPregnancies(selectedFarm),
          fetchAnimalsByFarm(selectedFarm)
        ])
        .then(() => {
          console.log('Successfully fetched pregnancies and animals for farm:', selectedFarm);
        })
        .catch(error => {
          console.error('Error fetching farm data:', error);
        })
        .finally(() => {
          clearTimeout(timeoutId);
          setLoading(false);
        });
      }

      // Cleanup function to clear timeout if component unmounts
      return () => {
        clearTimeout(timeoutId);
      };
    } else if (selectedFarm && farms.length === 0) {
      setLoading(false);
    }
  }, [selectedFarm, fetchPregnancies, fetchAnimalsByFarm]);
  
  // Memoize female animals to prevent infinite re-renders
  const femaleAnimals = useMemo(() => {
    if (animals && animals.length > 0) {
      // Filter for female animals
      const females = animals.filter(animal =>
        animal.gender === 'female'
      );

      return females.map(animal => ({
        id: animal.id,
        label: animal.name || animal.tagId || 'Unknown',
        imageUri: animal.imageUri ? { uri: animal.imageUri } : undefined
      }));
    }
    return [];
  }, [animals]);

  // Auto-select animal if provided in params
  useEffect(() => {
    if (initialAnimalId && !selectedFemaleAnimal && femaleAnimals.length > 0) {
      const targetAnimal = femaleAnimals.find(animal => animal.id === initialAnimalId);
      if (targetAnimal) {
        setSelectedFemaleAnimal(targetAnimal);
      }
    }
  }, [initialAnimalId, selectedFemaleAnimal, femaleAnimals]);

  // Filter pregnancies based on selected animal
  const filteredPregnancies = selectedFemaleAnimal 
    ? pregnancies.filter(p => p.animalId === selectedFemaleAnimal.id)
    : pregnancies;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 16,
      backgroundColor: themedColors.background,
    },
    listContent: {
      paddingBottom: 80,
    },
    addButton: {
      position: 'absolute',
      bottom: 20,
      right: 20,
      width: 56,
      height: 56,
      borderRadius: 28,
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 5,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    emptyState: {
      flex: 1,
      alignItems: 'center',
    },
    emptyStateText: {
      fontSize: 16,
      textAlign: 'center',
    },
    label: {
      fontSize: 16,
      marginBottom: 5,
      color: themedColors.text,
    },
    urduText: {
      fontFamily: 'UrduFont',
    },
  });

  // Combined loading state with safety checks
  const isLoading = loading || farmsLoading || pregnanciesLoading || animalsLoading;

  // Show empty state if no farms are available
  if (farms.length === 0 && !farmsLoading) {
    return (
      <View style={[styles.container, { backgroundColor: themedColors.background }]}>
        <View style={styles.emptyState}>
          <EmptyState
            icon={<MapPin size={48} color={themedColors.primary} />}
            title={t('farms.noFarms')}
            message={t('farms.noFarmsMessage')}
            actionLabel={t('farms.addFarm')}
            onAction={() => router.push('/farms/add')}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: themedColors.background }]}>
      {/* Uncomment the line below for debugging */}
      {/* <DebugInfo title="Pregnancy Debug" data={{ loading, farmsLoading, pregnanciesLoading, animalsLoading, isLoading, selectedFarm, farmsCount: farms.length, pregnanciesCount: pregnancies.length }} /> */}

      <Text style={[styles.label, language === 'ur' ? styles.urduText : null, { marginBottom: 10 }]}>
        {t('farms.selectFarm')}
      </Text>
      <GenericDropdown
        placeholder={t('farms.selectFarmPlaceholder')}
        items={farmOptions}
        value={selectedFarm}
        onSelect={(id) => {
          setSelectedFarm(id);
          setSelectedFemaleAnimal(null); // Reset animal selection when farm changes
        }}
        modalTitle={t('farms.selectFarm')}
        searchPlaceholder={t('farms.searchFarms')}
        renderIcon={false}
        containerStyle={{ maxHeight: 50, minHeight: 50, marginBottom: 15 }}
      />

      <Text style={[styles.label, language === 'ur' ? styles.urduText : null, { marginBottom: 5 }]}>
        {t('pregnancy.selectFemaleAnimal')}
      </Text>
      <GenericDropdown
        items={femaleAnimals}
        onSelect={(id) => {
          const selected = femaleAnimals.find(animal => animal.id === id);
          setSelectedFemaleAnimal(selected || null);
        }}
        value={selectedFemaleAnimal?.id}
        placeholder={t('pregnancy.selectFemalePlaceholder')}
        containerStyle={{ marginBottom: 15, maxHeight: 50, minHeight: 50 }}
      />

      {isLoading ? (
        <View style={styles.emptyState}>
        <LoadingIndicator fullScreen message={t('pregnancy.loading')} />
        </View>
      ) : filteredPregnancies.length > 0 ? (
        <FlatList
          data={filteredPregnancies}
          renderItem={({ item }) => (
            <PregnancyCard
              pregnancy={item}
              onPress={() => handlePregnancyPress(item)}
            />
          )}
          keyExtractor={(item) => item.id || item.animalId}
          contentContainerStyle={styles.listContent}
        />
      ) : (
        <View style={styles.emptyState}>
          <EmptyState
            icon={<Calendar size={48} color={themedColors.primary} />}
            title={t('pregnancy.noPregnancies')}
            message={t('pregnancy.noPregnanciesMessage')}
            actionLabel={t('pregnancy.addPregnancy')}
            onAction={() => router.push('/pregnancy/add')}
          />
        </View>
      )}

      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: themedColors.primary }]}
        onPress={() => { 
          router.push('/pregnancy/add'); 
        }}
      >
        <PlusIcon size={24} color="white" />
      </TouchableOpacity>
    </View>
  );
};

export default PregnancyListScreen;













